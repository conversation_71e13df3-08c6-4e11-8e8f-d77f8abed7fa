export interface Testimonial {
  id: string;
  name: string;
  position: string;
  company: string;
  companyLogo: string;
  testimonial: string;
  rating: number;
  project: string;
  image: string;
  industry: string;
}

export const testimonials: Testimonial[] = [
  {
    id: 'testimonial-1',
    name: '<PERSON>',
    position: 'CEO',
    company: 'RetailCorp',
    companyLogo: '/images/logos/retailcorp.png',
    testimonial: 'The team delivered an exceptional e-commerce platform that exceeded our expectations. Our conversion rates increased by 300% and the user experience is phenomenal. Their attention to detail and technical expertise is unmatched.',
    rating: 5,
    project: 'E-Commerce Platform',
    image: '/images/testimonials/sarah-johnson.jpg',
    industry: 'E-Commerce'
  },
  {
    id: 'testimonial-2',
    name: 'Dr. <PERSON>',
    position: 'CTO',
    company: 'MedTech Solutions',
    companyLogo: '/images/logos/medtech.png',
    testimonial: 'Working with this team was a game-changer for our healthcare practice. The mobile app they developed is HIPAA-compliant, user-friendly, and has significantly improved our patient engagement. Highly recommended!',
    rating: 5,
    project: 'Healthcare Management App',
    image: '/images/testimonials/michael-chen.jpg',
    industry: 'Healthcare'
  },
  {
    id: 'testimonial-3',
    name: '<PERSON>',
    position: 'Head of Operations',
    company: 'InvestPro',
    companyLogo: '/images/logos/investpro.png',
    testimonial: 'The analytics dashboard they built for us is incredible. Real-time data visualization and reporting capabilities have transformed how we make investment decisions. The ROI was immediate and substantial.',
    rating: 5,
    project: 'FinTech Analytics Dashboard',
    image: '/images/testimonials/emily-rodriguez.jpg',
    industry: 'Financial Services'
  },
  {
    id: 'testimonial-4',
    name: 'James Wilson',
    position: 'Logistics Director',
    company: 'LogiFlow',
    companyLogo: '/images/logos/logiflow.png',
    testimonial: 'The logistics management system revolutionized our operations. Route optimization and real-time tracking have reduced our costs by 25% while improving delivery times. Outstanding work!',
    rating: 5,
    project: 'Logistics Management System',
    image: '/images/testimonials/james-wilson.jpg',
    industry: 'Logistics'
  },
  {
    id: 'testimonial-5',
    name: 'Dr. Lisa Thompson',
    position: 'Academic Director',
    company: 'EduTech Institute',
    companyLogo: '/images/logos/edutech.png',
    testimonial: 'The online learning platform exceeded all our requirements. Student engagement increased by 200% and the interactive features make learning enjoyable. The team was professional and delivered on time.',
    rating: 5,
    project: 'Online Learning Platform',
    image: '/images/testimonials/lisa-thompson.jpg',
    industry: 'Education'
  },
  {
    id: 'testimonial-6',
    name: 'Robert Kim',
    position: 'Facilities Manager',
    company: 'SmartBuildings Inc',
    companyLogo: '/images/logos/smartbuildings.png',
    testimonial: 'The IoT dashboard provides incredible insights into our building operations. We\'ve reduced energy costs by 30% and maintenance issues are resolved 90% faster. Exceptional technical solution!',
    rating: 5,
    project: 'IoT Monitoring Dashboard',
    image: '/images/testimonials/robert-kim.jpg',
    industry: 'Real Estate'
  }
];

export const clientLogos = [
  { name: 'RetailCorp', logo: '/images/logos/retailcorp.png' },
  { name: 'MedTech Solutions', logo: '/images/logos/medtech.png' },
  { name: 'InvestPro', logo: '/images/logos/investpro.png' },
  { name: 'LogiFlow', logo: '/images/logos/logiflow.png' },
  { name: 'EduTech Institute', logo: '/images/logos/edutech.png' },
  { name: 'SmartBuildings Inc', logo: '/images/logos/smartbuildings.png' },
  { name: 'TechCorp', logo: '/images/logos/techcorp.png' },
  { name: 'InnovateLab', logo: '/images/logos/innovatelab.png' },
  { name: 'DataFlow', logo: '/images/logos/dataflow.png' },
  { name: 'CloudTech', logo: '/images/logos/cloudtech.png' },
];

export const industries = [
  'E-Commerce',
  'Healthcare',
  'Financial Services',
  'Education',
  'Logistics',
  'Real Estate',
  'Manufacturing',
  'Technology',
  'Retail',
  'Hospitality'
];
