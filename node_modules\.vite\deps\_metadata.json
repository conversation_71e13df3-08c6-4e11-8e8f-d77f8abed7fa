{"hash": "0010d867", "configHash": "545383df", "lockfileHash": "50a7567b", "browserHash": "f1430fec", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "eff049ff", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "5815def9", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "b4884a70", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "59d88c37", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "b0fc111a", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "3ac18527", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "22794b28", "needsInterop": true}, "react-intersection-observer": {"src": "../../react-intersection-observer/dist/index.mjs", "file": "react-intersection-observer.js", "fileHash": "1db0d4af", "needsInterop": false}}, "chunks": {"chunk-2ZET3HRN": {"file": "chunk-2ZET3HRN.js"}, "chunk-S3Z6QACX": {"file": "chunk-S3Z6QACX.js"}, "chunk-IYDKXRZQ": {"file": "chunk-IYDKXRZQ.js"}}}