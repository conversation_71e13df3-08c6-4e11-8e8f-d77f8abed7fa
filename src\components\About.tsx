import React from 'react';
import { motion } from 'framer-motion';
import { Users, Award, Clock, Globe, CheckCircle, ArrowRight } from 'lucide-react';
import Card from './ui/Card';
import Button from './ui/Button';
import AnimatedSection from './ui/AnimatedSection';

const About: React.FC = () => {
  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const stats = [
    { icon: Users, value: '500+', label: 'Projects Completed', color: 'text-blue-600' },
    { icon: Award, value: '98%', label: 'Client Satisfaction', color: 'text-green-600' },
    { icon: Clock, value: '100%', label: 'On-Time Delivery', color: 'text-purple-600' },
    { icon: Globe, value: '25+', label: 'Countries Served', color: 'text-orange-600' },
  ];

  const values = [
    {
      title: 'Global Standards',
      description: 'We follow international best practices and coding standards to ensure your project meets global quality benchmarks.',
      icon: Globe
    },
    {
      title: 'On-Time Delivery',
      description: 'We pride ourselves on delivering projects on schedule without compromising quality. Your deadlines are our commitment.',
      icon: Clock
    },
    {
      title: 'Technical Excellence',
      description: 'Our team stays updated with the latest technologies and frameworks to provide cutting-edge solutions.',
      icon: Award
    },
    {
      title: 'Client-Centric Approach',
      description: 'We prioritize clear communication, regular updates, and collaborative development throughout the project lifecycle.',
      icon: Users
    }
  ];

  const process = [
    {
      step: '01',
      title: 'Discovery & Planning',
      description: 'We start by understanding your business goals, target audience, and project requirements through detailed consultation.',
    },
    {
      step: '02',
      title: 'Design & Prototyping',
      description: 'Our design team creates wireframes and prototypes that align with your brand and provide exceptional user experience.',
    },
    {
      step: '03',
      title: 'Development & Testing',
      description: 'We build your solution using agile methodology with regular testing to ensure quality and performance.',
    },
    {
      step: '04',
      title: 'Launch & Support',
      description: 'We deploy your project and provide ongoing support to ensure smooth operation and continuous improvement.',
    }
  ];

  return (
    <section id="about" className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <AnimatedSection animation="slideUp" className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-6">
            About Us
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-display font-bold mb-6">
            Your Trusted Partner for
            <span className="text-gradient block">Digital Excellence</span>
          </h2>
          <p className="text-lg text-neutral-600 max-w-3xl mx-auto">
            We are a team of passionate developers, designers, and strategists committed to 
            transforming your digital vision into reality with unmatched quality and precision.
          </p>
        </AnimatedSection>

        {/* Stats Section */}
        <AnimatedSection animation="slideUp" delay={0.2}>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <div className={`w-16 h-16 mx-auto mb-4 rounded-xl bg-neutral-100 flex items-center justify-center ${stat.color}`}>
                  <stat.icon size={32} />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-neutral-900 mb-2">
                  {stat.value}
                </div>
                <div className="text-sm text-neutral-600">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </AnimatedSection>

        {/* Values Section */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          <AnimatedSection animation="slideRight">
            <h3 className="text-2xl md:text-3xl font-display font-bold mb-6">
              Why Choose AgencyPro?
            </h3>
            <p className="text-lg text-neutral-600 mb-8">
              We combine technical expertise with creative innovation to deliver solutions 
              that not only meet your requirements but exceed your expectations.
            </p>
            <div className="space-y-6">
              {values.map((value, index) => (
                <motion.div
                  key={value.title}
                  className="flex items-start"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <value.icon className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold mb-2">{value.title}</h4>
                    <p className="text-neutral-600">{value.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </AnimatedSection>

          <AnimatedSection animation="slideLeft">
            <Card className="bg-gradient-to-br from-primary-50 to-accent-50 border-primary-200">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-primary-600 to-accent-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <CheckCircle className="w-10 h-10 text-white" />
                </div>
                <h4 className="text-xl font-semibold mb-4">Quality Guarantee</h4>
                <p className="text-neutral-600 mb-6">
                  We stand behind our work with a 100% satisfaction guarantee. 
                  If you're not completely satisfied, we'll make it right.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                    <span className="text-sm">30-day money-back guarantee</span>
                  </div>
                  <div className="flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                    <span className="text-sm">Free revisions included</span>
                  </div>
                  <div className="flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                    <span className="text-sm">24/7 support available</span>
                  </div>
                </div>
              </div>
            </Card>
          </AnimatedSection>
        </div>

        {/* Process Section */}
        <AnimatedSection animation="slideUp">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-display font-bold mb-4">
              Our Development Process
            </h3>
            <p className="text-lg text-neutral-600">
              A proven methodology that ensures successful project delivery every time.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {process.map((item, index) => (
              <motion.div
                key={item.step}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="text-center h-full">
                  <div className="text-3xl font-bold text-primary-600 mb-4">
                    {item.step}
                  </div>
                  <h4 className="text-lg font-semibold mb-3">{item.title}</h4>
                  <p className="text-neutral-600 text-sm">{item.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </AnimatedSection>

        {/* CTA Section */}
        <AnimatedSection animation="slideUp" className="text-center">
          <div className="bg-neutral-50 rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-display font-bold mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-lg text-neutral-600 mb-8">
              Let's discuss your project and see how we can help you achieve your digital goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="primary"
                size="lg"
                icon={ArrowRight}
                iconPosition="right"
                onClick={() => scrollToSection('#contact')}
              >
                Start Your Project
              </Button>
              <Button
                variant="secondary"
                size="lg"
                onClick={() => scrollToSection('#portfolio')}
              >
                View Our Work
              </Button>
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
};

export default About;
