import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Play, Star, Users, Award, Zap } from 'lucide-react';
import Button from './ui/Button';
import AnimatedSection from './ui/AnimatedSection';

const Hero: React.FC = () => {
  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const stats = [
    { icon: Users, value: '500+', label: 'Happy Clients' },
    { icon: Award, value: '98%', label: 'Success Rate' },
    { icon: Zap, value: '24/7', label: 'Support' },
    { icon: Star, value: '4.9/5', label: 'Client Rating' },
  ];

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-neutral-50 via-white to-primary-50">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-primary-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="container-custom relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Left Column - Content */}
          <div className="text-center lg:text-left">
            <AnimatedSection animation="slideUp" delay={0.2}>
              <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-6">
                <Star className="w-4 h-4 mr-2 fill-current" />
                Trusted by 500+ Global Clients
              </div>
            </AnimatedSection>

            <AnimatedSection animation="slideUp" delay={0.4}>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-display font-bold leading-tight mb-6">
                Transform Your
                <span className="text-gradient block">Digital Vision</span>
                Into Reality
              </h1>
            </AnimatedSection>

            <AnimatedSection animation="slideUp" delay={0.6}>
              <p className="text-lg md:text-xl text-neutral-600 mb-8 max-w-2xl mx-auto lg:mx-0">
                We deliver world-class web design, mobile apps, and custom software solutions 
                that drive growth and exceed expectations. On-time delivery guaranteed.
              </p>
            </AnimatedSection>

            <AnimatedSection animation="slideUp" delay={0.8}>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12">
                <Button
                  variant="primary"
                  size="lg"
                  icon={ArrowRight}
                  iconPosition="right"
                  onClick={() => scrollToSection('#contact')}
                >
                  Start Your Project
                </Button>
                <Button
                  variant="secondary"
                  size="lg"
                  icon={Play}
                  onClick={() => scrollToSection('#portfolio')}
                >
                  View Our Work
                </Button>
              </div>
            </AnimatedSection>

            {/* Stats */}
            <AnimatedSection animation="slideUp" delay={1.0}>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {stats.map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    className="text-center lg:text-left"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 1.2 + index * 0.1 }}
                  >
                    <div className="flex items-center justify-center lg:justify-start mb-2">
                      <stat.icon className="w-6 h-6 text-primary-600 mr-2" />
                      <span className="text-2xl md:text-3xl font-bold text-neutral-900">
                        {stat.value}
                      </span>
                    </div>
                    <p className="text-sm text-neutral-600">{stat.label}</p>
                  </motion.div>
                ))}
              </div>
            </AnimatedSection>
          </div>

          {/* Right Column - Visual */}
          <div className="relative">
            <AnimatedSection animation="slideLeft" delay={0.6}>
              <div className="relative">
                {/* Main Image Placeholder */}
                <div className="relative bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl p-8 shadow-large">
                  <div className="bg-white rounded-xl p-6 shadow-medium">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                          <Zap className="w-6 h-6 text-primary-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-neutral-900">Lightning Fast</h3>
                          <p className="text-sm text-neutral-600">Optimized Performance</p>
                        </div>
                      </div>
                      <div className="h-2 bg-neutral-200 rounded-full overflow-hidden">
                        <motion.div
                          className="h-full bg-gradient-to-r from-primary-500 to-accent-500"
                          initial={{ width: 0 }}
                          animate={{ width: '95%' }}
                          transition={{ delay: 1.5, duration: 1.5 }}
                        />
                      </div>
                      <div className="grid grid-cols-3 gap-3">
                        <div className="h-16 bg-neutral-100 rounded-lg"></div>
                        <div className="h-16 bg-primary-100 rounded-lg"></div>
                        <div className="h-16 bg-accent-100 rounded-lg"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Floating Elements */}
                <motion.div
                  className="absolute -top-6 -right-6 bg-white rounded-xl p-4 shadow-large"
                  animate={{ y: [0, -10, 0] }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium">Live</span>
                  </div>
                </motion.div>

                <motion.div
                  className="absolute -bottom-6 -left-6 bg-white rounded-xl p-4 shadow-large"
                  animate={{ y: [0, 10, 0] }}
                  transition={{ duration: 3, repeat: Infinity, delay: 1.5 }}
                >
                  <div className="flex items-center space-x-2">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium">5.0 Rating</span>
                  </div>
                </motion.div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <div className="w-6 h-10 border-2 border-neutral-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-neutral-400 rounded-full mt-2"></div>
        </div>
      </motion.div>
    </section>
  );
};

export default Hero;
