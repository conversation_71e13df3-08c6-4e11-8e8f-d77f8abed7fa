import React from 'react';

const Hero: React.FC = () => {
  return (
    <section id="home" className="min-h-screen bg-blue-50 flex items-center justify-center p-8">
      <div className="text-center max-w-4xl mx-auto">
        <h1 className="text-6xl font-bold text-gray-900 mb-6">
          Transform Your
          <span className="text-gradient block">Digital Vision</span>
          Into Reality
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          We deliver world-class web design, mobile apps, and custom software solutions
          that drive growth and exceed expectations. On-time delivery guaranteed.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors">
            Start Your Project
          </button>
          <button className="bg-white hover:bg-gray-50 text-blue-600 font-semibold py-3 px-8 rounded-lg border-2 border-blue-600 transition-colors">
            View Our Work
          </button>
        </div>
      </div>
    </section>
  );
};

export default Hero;
