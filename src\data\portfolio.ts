export interface PortfolioProject {
  id: string;
  title: string;
  category: string;
  description: string;
  challenge: string;
  solution: string;
  results: string[];
  technologies: string[];
  image: string;
  liveUrl?: string;
  caseStudyUrl?: string;
  client: string;
  duration: string;
  year: string;
}

export const portfolioProjects: PortfolioProject[] = [
  {
    id: 'ecommerce-platform',
    title: 'E-Commerce Platform',
    category: 'Web Development',
    description: 'A comprehensive e-commerce solution with advanced inventory management, payment processing, and analytics.',
    challenge: 'The client needed a scalable e-commerce platform that could handle high traffic and complex inventory management.',
    solution: 'Built a modern React-based platform with microservices architecture, integrated payment gateways, and real-time analytics.',
    results: [
      '300% increase in conversion rate',
      '50% reduction in page load time',
      '99.9% uptime achieved',
      '$2M+ in sales processed'
    ],
    technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'AWS', 'Redis'],
    image: '/images/portfolio/ecommerce.jpg',
    liveUrl: 'https://example-ecommerce.com',
    caseStudyUrl: '#case-study-1',
    client: 'RetailCorp',
    duration: '4 months',
    year: '2024'
  },
  {
    id: 'healthcare-app',
    title: 'Healthcare Management App',
    category: 'Mobile Development',
    description: 'A comprehensive healthcare app for patient management, appointment scheduling, and telemedicine.',
    challenge: 'Healthcare providers needed a secure, HIPAA-compliant solution for remote patient care and management.',
    solution: 'Developed a cross-platform mobile app with end-to-end encryption, video calling, and integrated EHR systems.',
    results: [
      '80% reduction in appointment no-shows',
      '95% patient satisfaction rate',
      '60% increase in provider efficiency',
      'HIPAA compliance achieved'
    ],
    technologies: ['Flutter', 'Firebase', 'WebRTC', 'Node.js', 'PostgreSQL'],
    image: '/images/portfolio/healthcare.jpg',
    liveUrl: 'https://healthcare-app.com',
    caseStudyUrl: '#case-study-2',
    client: 'MedTech Solutions',
    duration: '6 months',
    year: '2024'
  },
  {
    id: 'fintech-dashboard',
    title: 'FinTech Analytics Dashboard',
    category: 'Web Application',
    description: 'Real-time financial analytics dashboard with advanced data visualization and reporting capabilities.',
    challenge: 'Financial institution needed real-time insights into market data and portfolio performance with complex visualizations.',
    solution: 'Created a sophisticated dashboard with real-time data processing, interactive charts, and customizable reports.',
    results: [
      '40% faster decision making',
      '90% reduction in report generation time',
      '100% real-time data accuracy',
      '$500K cost savings annually'
    ],
    technologies: ['Vue.js', 'D3.js', 'Python', 'FastAPI', 'PostgreSQL', 'Docker'],
    image: '/images/portfolio/fintech.jpg',
    liveUrl: 'https://fintech-dashboard.com',
    caseStudyUrl: '#case-study-3',
    client: 'InvestPro',
    duration: '5 months',
    year: '2023'
  },
  {
    id: 'logistics-system',
    title: 'Logistics Management System',
    category: 'Software Solution',
    description: 'End-to-end logistics management system with route optimization, tracking, and automated reporting.',
    challenge: 'Logistics company struggled with manual processes, inefficient routing, and lack of real-time visibility.',
    solution: 'Built a comprehensive system with AI-powered route optimization, real-time tracking, and automated workflows.',
    results: [
      '35% reduction in delivery time',
      '25% fuel cost savings',
      '99% delivery accuracy',
      '50% reduction in manual work'
    ],
    technologies: ['React', 'Python', 'Machine Learning', 'PostgreSQL', 'Google Maps API'],
    image: '/images/portfolio/logistics.jpg',
    liveUrl: 'https://logistics-system.com',
    caseStudyUrl: '#case-study-4',
    client: 'LogiFlow',
    duration: '8 months',
    year: '2023'
  },
  {
    id: 'education-platform',
    title: 'Online Learning Platform',
    category: 'Web Development',
    description: 'Interactive online learning platform with video streaming, assessments, and progress tracking.',
    challenge: 'Educational institution needed a scalable platform for remote learning with interactive features.',
    solution: 'Developed a feature-rich LMS with video streaming, interactive quizzes, and comprehensive analytics.',
    results: [
      '200% increase in student engagement',
      '85% course completion rate',
      '95% user satisfaction',
      '10,000+ active users'
    ],
    technologies: ['Next.js', 'Node.js', 'MongoDB', 'WebRTC', 'AWS S3'],
    image: '/images/portfolio/education.jpg',
    liveUrl: 'https://learning-platform.com',
    caseStudyUrl: '#case-study-5',
    client: 'EduTech Institute',
    duration: '6 months',
    year: '2023'
  },
  {
    id: 'iot-dashboard',
    title: 'IoT Monitoring Dashboard',
    category: 'Web Application',
    description: 'Real-time IoT device monitoring and control dashboard for smart building management.',
    challenge: 'Building management company needed centralized control and monitoring of IoT devices across multiple properties.',
    solution: 'Created a real-time dashboard with device management, alerts, and predictive maintenance features.',
    results: [
      '30% reduction in energy costs',
      '90% faster issue resolution',
      '99.5% device uptime',
      '40% reduction in maintenance costs'
    ],
    technologies: ['React', 'Socket.io', 'Node.js', 'InfluxDB', 'MQTT'],
    image: '/images/portfolio/iot.jpg',
    liveUrl: 'https://iot-dashboard.com',
    caseStudyUrl: '#case-study-6',
    client: 'SmartBuildings Inc',
    duration: '4 months',
    year: '2024'
  }
];
