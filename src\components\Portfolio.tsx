import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ExternalLink, ArrowRight, Calendar, User, TrendingUp } from 'lucide-react';
import { portfolioProjects } from '../data/portfolio';
import Card from './ui/Card';
import Button from './ui/Button';
import AnimatedSection from './ui/AnimatedSection';

const Portfolio: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedProject, setSelectedProject] = useState<string | null>(null);

  const categories = ['All', ...Array.from(new Set(portfolioProjects.map(p => p.category)))];
  
  const filteredProjects = selectedCategory === 'All' 
    ? portfolioProjects 
    : portfolioProjects.filter(p => p.category === selectedCategory);

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="portfolio" className="section-padding bg-neutral-50">
      <div className="container-custom">
        {/* Section Header */}
        <AnimatedSection animation="slideUp" className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-accent-100 text-accent-700 rounded-full text-sm font-medium mb-6">
            Our Work
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-display font-bold mb-6">
            Portfolio That Speaks
            <span className="text-gradient block">Success Stories</span>
          </h2>
          <p className="text-lg text-neutral-600 max-w-3xl mx-auto">
            Explore our diverse portfolio of successful projects that have transformed 
            businesses and delivered exceptional results for our clients.
          </p>
        </AnimatedSection>

        {/* Category Filter */}
        <AnimatedSection animation="slideUp" delay={0.2}>
          <div className="flex flex-wrap justify-center gap-3 mb-12">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                  selectedCategory === category
                    ? 'bg-primary-600 text-white shadow-medium'
                    : 'bg-white text-neutral-700 hover:bg-primary-50 hover:text-primary-600 shadow-soft'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </AnimatedSection>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <AnimatePresence mode="wait">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card
                  className="group cursor-pointer overflow-hidden"
                  onClick={() => setSelectedProject(project.id)}
                >
                  {/* Project Image */}
                  <div className="relative h-48 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg mb-4 overflow-hidden">
                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-white text-center">
                        <div className="text-4xl font-bold mb-2">{project.title.split(' ')[0]}</div>
                        <div className="text-sm opacity-90">{project.category}</div>
                      </div>
                    </div>
                    <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <ExternalLink className="w-6 h-6 text-white" />
                    </div>
                  </div>

                  {/* Project Info */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-primary-600 font-medium">{project.category}</span>
                      <span className="text-sm text-neutral-500">{project.year}</span>
                    </div>
                    <h3 className="text-xl font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                      {project.title}
                    </h3>
                    <p className="text-neutral-600 text-sm mb-4 line-clamp-2">
                      {project.description}
                    </p>
                    
                    {/* Client & Duration */}
                    <div className="flex items-center justify-between text-sm text-neutral-500 mb-4">
                      <div className="flex items-center">
                        <User className="w-4 h-4 mr-1" />
                        {project.client}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {project.duration}
                      </div>
                    </div>

                    {/* Technologies */}
                    <div className="flex flex-wrap gap-1 mb-4">
                      {project.technologies.slice(0, 3).map((tech) => (
                        <span
                          key={tech}
                          className="px-2 py-1 bg-neutral-100 text-neutral-600 rounded text-xs"
                        >
                          {tech}
                        </span>
                      ))}
                      {project.technologies.length > 3 && (
                        <span className="px-2 py-1 bg-neutral-100 text-neutral-600 rounded text-xs">
                          +{project.technologies.length - 3}
                        </span>
                      )}
                    </div>

                    {/* Key Result */}
                    <div className="flex items-center text-green-600 text-sm font-medium">
                      <TrendingUp className="w-4 h-4 mr-1" />
                      {project.results[0]}
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Project Modal */}
        <AnimatePresence>
          {selectedProject && (
            <motion.div
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setSelectedProject(null)}
            >
              <motion.div
                className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
              >
                {(() => {
                  const project = portfolioProjects.find(p => p.id === selectedProject);
                  if (!project) return null;

                  return (
                    <div className="p-8">
                      <div className="flex items-center justify-between mb-6">
                        <div>
                          <h3 className="text-2xl font-bold mb-2">{project.title}</h3>
                          <div className="flex items-center space-x-4 text-sm text-neutral-600">
                            <span className="bg-primary-100 text-primary-700 px-3 py-1 rounded-full">
                              {project.category}
                            </span>
                            <span>{project.client}</span>
                            <span>{project.year}</span>
                          </div>
                        </div>
                        <button
                          onClick={() => setSelectedProject(null)}
                          className="text-neutral-400 hover:text-neutral-600 text-2xl"
                        >
                          ×
                        </button>
                      </div>

                      <div className="grid md:grid-cols-2 gap-8">
                        <div>
                          <h4 className="text-lg font-semibold mb-3">Challenge</h4>
                          <p className="text-neutral-600 mb-6">{project.challenge}</p>

                          <h4 className="text-lg font-semibold mb-3">Solution</h4>
                          <p className="text-neutral-600 mb-6">{project.solution}</p>

                          <h4 className="text-lg font-semibold mb-3">Technologies</h4>
                          <div className="flex flex-wrap gap-2 mb-6">
                            {project.technologies.map((tech) => (
                              <span
                                key={tech}
                                className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm"
                              >
                                {tech}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="text-lg font-semibold mb-3">Results</h4>
                          <div className="space-y-3 mb-6">
                            {project.results.map((result, index) => (
                              <div key={index} className="flex items-center">
                                <TrendingUp className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                                <span className="text-neutral-700">{result}</span>
                              </div>
                            ))}
                          </div>

                          <div className="flex space-x-4">
                            {project.liveUrl && (
                              <Button
                                variant="primary"
                                size="sm"
                                icon={ExternalLink}
                                href={project.liveUrl}
                                target="_blank"
                              >
                                View Live
                              </Button>
                            )}
                            <Button
                              variant="secondary"
                              size="sm"
                              onClick={() => scrollToSection('#contact')}
                            >
                              Similar Project
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* CTA Section */}
        <AnimatedSection animation="slideUp" className="text-center">
          <div className="bg-gradient-to-r from-primary-600 to-accent-600 rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-display font-bold mb-4">
              Ready to Start Your Project?
            </h3>
            <p className="text-lg mb-8 opacity-90">
              Let's discuss how we can bring your vision to life with our proven expertise.
            </p>
            <Button
              variant="secondary"
              size="lg"
              icon={ArrowRight}
              iconPosition="right"
              onClick={() => scrollToSection('#contact')}
            >
              Get Started Today
            </Button>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
};

export default Portfolio;
