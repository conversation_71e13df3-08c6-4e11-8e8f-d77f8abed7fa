@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', system-ui, sans-serif;
}

html {
  scroll-behavior: smooth;
}

.text-gradient {
  background: linear-gradient(to right, #0284c7, #c026d3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.container-custom {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding: 0 2rem;
  }
}

.section-padding {
  padding: 4rem 0;
}

@media (min-width: 768px) {
  .section-padding {
    padding: 6rem 0;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding: 8rem 0;
  }
}
