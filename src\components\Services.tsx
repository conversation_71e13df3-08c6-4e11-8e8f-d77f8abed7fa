import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, CheckCircle } from 'lucide-react';
import { services, techStack } from '../data/services';
import Card from './ui/Card';
import Button from './ui/Button';
import AnimatedSection from './ui/AnimatedSection';

const Services: React.FC = () => {
  const [selectedService, setSelectedService] = useState(services[0]);

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="services" className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <AnimatedSection animation="slideUp" className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-6">
            Our Expertise
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-display font-bold mb-6">
            Services That Drive
            <span className="text-gradient block">Digital Success</span>
          </h2>
          <p className="text-lg text-neutral-600 max-w-3xl mx-auto">
            From concept to deployment, we deliver comprehensive digital solutions 
            using cutting-edge technologies and industry best practices.
          </p>
        </AnimatedSection>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <AnimatedSection
                key={service.id}
                animation="slideUp"
                delay={index * 0.1}
              >
                <Card
                  className={`cursor-pointer transition-all duration-300 ${
                    selectedService.id === service.id
                      ? 'ring-2 ring-primary-500 bg-primary-50'
                      : 'hover:bg-neutral-50'
                  }`}
                  onClick={() => setSelectedService(service)}
                  hover={false}
                >
                  <div className="text-center">
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-xl flex items-center justify-center ${
                      selectedService.id === service.id
                        ? 'bg-primary-600 text-white'
                        : 'bg-primary-100 text-primary-600'
                    }`}>
                      <IconComponent size={32} />
                    </div>
                    <h3 className="text-lg font-semibold mb-2">{service.title}</h3>
                    <p className="text-sm text-neutral-600 line-clamp-3">
                      {service.description}
                    </p>
                  </div>
                </Card>
              </AnimatedSection>
            );
          })}
        </div>

        {/* Selected Service Details */}
        <AnimatedSection animation="fadeIn" key={selectedService.id}>
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Details */}
            <div>
              <div className="flex items-center mb-4">
                <selectedService.icon className="w-8 h-8 text-primary-600 mr-3" />
                <h3 className="text-2xl md:text-3xl font-display font-bold">
                  {selectedService.title}
                </h3>
              </div>
              
              <p className="text-lg text-neutral-600 mb-6">
                {selectedService.description}
              </p>

              <div className="mb-6">
                <h4 className="text-lg font-semibold mb-3 text-neutral-900">
                  Problem We Solve:
                </h4>
                <p className="text-neutral-600">{selectedService.problem}</p>
              </div>

              <div className="mb-8">
                <h4 className="text-lg font-semibold mb-4 text-neutral-900">
                  Key Features:
                </h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {selectedService.features.map((feature, index) => (
                    <motion.div
                      key={feature}
                      className="flex items-center"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-neutral-700">{feature}</span>
                    </motion.div>
                  ))}
                </div>
              </div>

              <Button
                variant="primary"
                icon={ArrowRight}
                iconPosition="right"
                onClick={() => scrollToSection('#contact')}
              >
                Get Started
              </Button>
            </div>

            {/* Right Column - Process & Technologies */}
            <div className="space-y-8">
              {/* Development Process */}
              <Card>
                <h4 className="text-xl font-semibold mb-4">Our Process</h4>
                <div className="space-y-4">
                  {selectedService.process.map((step, index) => (
                    <motion.div
                      key={step}
                      className="flex items-center"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mr-4 flex-shrink-0">
                        {index + 1}
                      </div>
                      <span className="text-neutral-700">{step}</span>
                    </motion.div>
                  ))}
                </div>
              </Card>

              {/* Technologies */}
              <Card>
                <h4 className="text-xl font-semibold mb-4">Technologies Used</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedService.technologies.map((tech, index) => (
                    <motion.span
                      key={tech}
                      className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      {tech}
                    </motion.span>
                  ))}
                </div>
              </Card>
            </div>
          </div>
        </AnimatedSection>

        {/* Tech Stack Section */}
        <AnimatedSection animation="slideUp" className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-display font-bold mb-4">
              Our Technology Stack
            </h3>
            <p className="text-lg text-neutral-600">
              We use cutting-edge technologies to build scalable, secure, and high-performance solutions.
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {techStack.map((tech, index) => (
              <motion.div
                key={tech.name}
                className="bg-white border border-neutral-200 rounded-lg p-4 text-center hover:shadow-medium transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
              >
                <div className="text-sm font-medium text-neutral-900 mb-1">
                  {tech.name}
                </div>
                <div className="text-xs text-neutral-500">{tech.category}</div>
              </motion.div>
            ))}
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
};

export default Services;
