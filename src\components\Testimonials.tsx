import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, Quote, ChevronLeft, ChevronRight } from 'lucide-react';
import { testimonials, clientLogos } from '../data/testimonials';
import Card from './ui/Card';
import AnimatedSection from './ui/AnimatedSection';

const Testimonials: React.FC = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-5 h-5 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <section id="testimonials" className="section-padding bg-neutral-50">
      <div className="container-custom">
        {/* Section Header */}
        <AnimatedSection animation="slideUp" className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-accent-100 text-accent-700 rounded-full text-sm font-medium mb-6">
            Client Success Stories
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-display font-bold mb-6">
            What Our Clients
            <span className="text-gradient block">Say About Us</span>
          </h2>
          <p className="text-lg text-neutral-600 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our satisfied clients have to say 
            about their experience working with us.
          </p>
        </AnimatedSection>

        {/* Main Testimonial Carousel */}
        <div className="relative mb-16">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentTestimonial}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.5 }}
              className="max-w-4xl mx-auto"
            >
              <Card className="text-center relative overflow-hidden">
                <div className="absolute top-6 left-6 text-primary-200">
                  <Quote size={48} />
                </div>
                
                <div className="pt-8">
                  {/* Rating */}
                  <div className="flex justify-center mb-6">
                    {renderStars(testimonials[currentTestimonial].rating)}
                  </div>

                  {/* Testimonial Text */}
                  <blockquote className="text-lg md:text-xl text-neutral-700 mb-8 leading-relaxed">
                    "{testimonials[currentTestimonial].testimonial}"
                  </blockquote>

                  {/* Client Info */}
                  <div className="flex items-center justify-center space-x-4">
                    <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      {testimonials[currentTestimonial].name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div className="text-left">
                      <div className="font-semibold text-neutral-900">
                        {testimonials[currentTestimonial].name}
                      </div>
                      <div className="text-sm text-neutral-600">
                        {testimonials[currentTestimonial].position} at {testimonials[currentTestimonial].company}
                      </div>
                      <div className="text-xs text-primary-600 font-medium">
                        {testimonials[currentTestimonial].project}
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          </AnimatePresence>

          {/* Navigation Buttons */}
          <button
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-medium hover:shadow-large transition-all duration-300 flex items-center justify-center text-neutral-600 hover:text-primary-600"
          >
            <ChevronLeft size={24} />
          </button>
          <button
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-medium hover:shadow-large transition-all duration-300 flex items-center justify-center text-neutral-600 hover:text-primary-600"
          >
            <ChevronRight size={24} />
          </button>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentTestimonial(index);
                  setIsAutoPlaying(false);
                }}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentTestimonial
                    ? 'bg-primary-600 w-8'
                    : 'bg-neutral-300 hover:bg-neutral-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* All Testimonials Grid */}
        <AnimatedSection animation="slideUp">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
            {testimonials.slice(0, 6).map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex">{renderStars(testimonial.rating)}</div>
                    <span className="text-xs text-neutral-500 bg-neutral-100 px-2 py-1 rounded">
                      {testimonial.industry}
                    </span>
                  </div>
                  
                  <blockquote className="text-neutral-700 mb-4 text-sm leading-relaxed line-clamp-3">
                    "{testimonial.testimonial}"
                  </blockquote>
                  
                  <div className="flex items-center space-x-3 mt-auto">
                    <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div>
                      <div className="font-medium text-sm text-neutral-900">
                        {testimonial.name}
                      </div>
                      <div className="text-xs text-neutral-600">
                        {testimonial.position}, {testimonial.company}
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </AnimatedSection>

        {/* Trusted By Section */}
        <AnimatedSection animation="slideUp">
          <div className="text-center mb-8">
            <h3 className="text-xl font-semibold text-neutral-900 mb-2">
              Trusted by Leading Companies
            </h3>
            <p className="text-neutral-600">
              Join 500+ satisfied clients who have transformed their business with us
            </p>
          </div>
          
          <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-10 gap-6 items-center opacity-60">
            {clientLogos.map((client, index) => (
              <motion.div
                key={client.name}
                className="flex items-center justify-center h-12"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 0.6, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ opacity: 1, scale: 1.1 }}
              >
                <div className="w-full h-8 bg-neutral-400 rounded flex items-center justify-center text-white text-xs font-medium">
                  {client.name.substring(0, 3).toUpperCase()}
                </div>
              </motion.div>
            ))}
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
};

export default Testimonials;
