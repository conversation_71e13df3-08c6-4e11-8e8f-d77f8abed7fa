import { Code, Smartphone, Globe, Cog } from 'lucide-react';

export interface Service {
  id: string;
  title: string;
  description: string;
  problem: string;
  technologies: string[];
  process: string[];
  icon: any;
  features: string[];
  caseStudyLink?: string;
}

export const services: Service[] = [
  {
    id: 'web-design',
    title: 'Web Design & Development',
    description: 'Create stunning, responsive websites that convert visitors into customers with modern design principles and cutting-edge technology.',
    problem: 'Many businesses struggle with outdated websites that don\'t reflect their brand or convert visitors effectively.',
    technologies: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS', 'Node.js', 'MongoDB'],
    process: [
      'Discovery & Strategy',
      'UI/UX Design',
      'Development & Testing',
      'Launch & Optimization'
    ],
    icon: Globe,
    features: [
      'Responsive Design',
      'SEO Optimized',
      'Fast Loading',
      'Modern UI/UX',
      'CMS Integration',
      'Analytics Setup'
    ],
    caseStudyLink: '#portfolio'
  },
  {
    id: 'mobile-app',
    title: 'Mobile App Development',
    description: 'Build native and cross-platform mobile applications that provide exceptional user experiences across iOS and Android.',
    problem: 'Companies need mobile presence but struggle with the complexity and cost of developing for multiple platforms.',
    technologies: ['Flutter', 'React Native', 'Swift', 'Kotlin', 'Firebase', 'AWS'],
    process: [
      'Market Research',
      'Prototype Design',
      'Development',
      'Testing & Launch'
    ],
    icon: Smartphone,
    features: [
      'Cross-Platform',
      'Native Performance',
      'Offline Capability',
      'Push Notifications',
      'App Store Optimization',
      'Analytics Integration'
    ],
    caseStudyLink: '#portfolio'
  },
  {
    id: 'web-app',
    title: 'Web Application Development',
    description: 'Develop scalable, secure web applications that streamline business processes and enhance productivity.',
    problem: 'Businesses need custom solutions to automate processes and improve efficiency but lack technical expertise.',
    technologies: ['React', 'Vue.js', 'Node.js', 'Python', 'PostgreSQL', 'Docker'],
    process: [
      'Requirements Analysis',
      'Architecture Design',
      'Agile Development',
      'Deployment & Support'
    ],
    icon: Code,
    features: [
      'Custom Solutions',
      'Scalable Architecture',
      'Security First',
      'API Integration',
      'Real-time Features',
      'Cloud Deployment'
    ],
    caseStudyLink: '#portfolio'
  },
  {
    id: 'software-solutions',
    title: 'Custom Software Solutions',
    description: 'Design and develop tailored software solutions that address specific business challenges and drive growth.',
    problem: 'Off-the-shelf software often doesn\'t meet unique business requirements, leading to inefficiencies.',
    technologies: ['Python', 'Java', 'C#', '.NET', 'Microservices', 'Kubernetes'],
    process: [
      'Business Analysis',
      'Solution Design',
      'Development & Testing',
      'Integration & Training'
    ],
    icon: Cog,
    features: [
      'Custom Development',
      'System Integration',
      'Automation',
      'Data Analytics',
      'Maintenance & Support',
      'Training & Documentation'
    ],
    caseStudyLink: '#portfolio'
  }
];

export const techStack = [
  { name: 'React', category: 'Frontend' },
  { name: 'Next.js', category: 'Frontend' },
  { name: 'Vue.js', category: 'Frontend' },
  { name: 'TypeScript', category: 'Language' },
  { name: 'Node.js', category: 'Backend' },
  { name: 'Python', category: 'Backend' },
  { name: 'Flutter', category: 'Mobile' },
  { name: 'React Native', category: 'Mobile' },
  { name: 'AWS', category: 'Cloud' },
  { name: 'Docker', category: 'DevOps' },
  { name: 'MongoDB', category: 'Database' },
  { name: 'PostgreSQL', category: 'Database' },
];
