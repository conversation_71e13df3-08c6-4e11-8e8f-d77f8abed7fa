import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Mail, 
  Phone, 
  MessageSquare, 
  Calendar, 
  Send, 
  CheckCircle,
  MapPin,
  Clock,
  Globe
} from 'lucide-react';
import Card from './ui/Card';
import Button from './ui/Button';
import AnimatedSection from './ui/AnimatedSection';

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    service: '',
    budget: '',
    timeline: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false);
      setFormData({
        name: '',
        email: '',
        company: '',
        service: '',
        budget: '',
        timeline: '',
        message: ''
      });
    }, 3000);
  };

  const contactMethods = [
    {
      icon: Mail,
      title: 'Email Us',
      description: 'Send us an email and we\'ll respond within 24 hours',
      value: '<EMAIL>',
      action: 'mailto:<EMAIL>',
      color: 'text-blue-600'
    },
    {
      icon: Phone,
      title: 'Call Us',
      description: 'Speak directly with our team during business hours',
      value: '+****************',
      action: 'tel:+***********',
      color: 'text-green-600'
    },
    {
      icon: MessageSquare,
      title: 'WhatsApp',
      description: 'Chat with us instantly on WhatsApp',
      value: '+****************',
      action: 'https://wa.me/***********',
      color: 'text-green-500'
    },
    {
      icon: Calendar,
      title: 'Schedule Call',
      description: 'Book a free consultation call at your convenience',
      value: 'Book Meeting',
      action: 'https://calendly.com/agencypro',
      color: 'text-purple-600'
    }
  ];

  const officeInfo = [
    {
      icon: MapPin,
      title: 'Office Location',
      value: 'San Francisco, CA, USA'
    },
    {
      icon: Clock,
      title: 'Business Hours',
      value: 'Mon-Fri: 9AM-6PM PST'
    },
    {
      icon: Globe,
      title: 'Global Reach',
      value: '25+ Countries Served'
    }
  ];

  return (
    <section id="contact" className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <AnimatedSection animation="slideUp" className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-6">
            Get In Touch
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-display font-bold mb-6">
            Let's Start Your
            <span className="text-gradient block">Next Project</span>
          </h2>
          <p className="text-lg text-neutral-600 max-w-3xl mx-auto">
            Ready to transform your digital presence? Get in touch with us today for a free 
            consultation and let's discuss how we can bring your vision to life.
          </p>
        </AnimatedSection>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Form */}
          <AnimatedSection animation="slideRight">
            <Card className="lg:sticky lg:top-8">
              <h3 className="text-2xl font-semibold mb-6">Send us a message</h3>
              
              {isSubmitted ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center py-8"
                >
                  <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                  <h4 className="text-xl font-semibold text-green-600 mb-2">
                    Message Sent Successfully!
                  </h4>
                  <p className="text-neutral-600">
                    Thank you for reaching out. We'll get back to you within 24 hours.
                  </p>
                </motion.div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                        placeholder="John Doe"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Company Name
                    </label>
                    <input
                      type="text"
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                      placeholder="Your Company"
                    />
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Service Needed
                      </label>
                      <select
                        name="service"
                        value={formData.service}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                      >
                        <option value="">Select a service</option>
                        <option value="web-design">Web Design & Development</option>
                        <option value="mobile-app">Mobile App Development</option>
                        <option value="web-app">Web Application</option>
                        <option value="software">Custom Software</option>
                        <option value="consultation">Consultation</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Budget Range
                      </label>
                      <select
                        name="budget"
                        value={formData.budget}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                      >
                        <option value="">Select budget</option>
                        <option value="5k-10k">$5,000 - $10,000</option>
                        <option value="10k-25k">$10,000 - $25,000</option>
                        <option value="25k-50k">$25,000 - $50,000</option>
                        <option value="50k+">$50,000+</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Project Timeline
                    </label>
                    <select
                      name="timeline"
                      value={formData.timeline}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                    >
                      <option value="">Select timeline</option>
                      <option value="asap">ASAP</option>
                      <option value="1-2months">1-2 months</option>
                      <option value="3-6months">3-6 months</option>
                      <option value="6months+">6+ months</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Project Details *
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={4}
                      className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 resize-none"
                      placeholder="Tell us about your project requirements, goals, and any specific features you need..."
                    />
                  </div>

                  <Button
                    type="submit"
                    variant="primary"
                    size="lg"
                    icon={Send}
                    iconPosition="right"
                    disabled={isSubmitting}
                    className="w-full"
                  >
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </Button>
                </form>
              )}
            </Card>
          </AnimatedSection>

          {/* Contact Information */}
          <div className="space-y-8">
            {/* Contact Methods */}
            <AnimatedSection animation="slideLeft">
              <h3 className="text-2xl font-semibold mb-6">Get in touch</h3>
              <div className="grid gap-4">
                {contactMethods.map((method, index) => (
                  <motion.a
                    key={method.title}
                    href={method.action}
                    target={method.action.startsWith('http') ? '_blank' : '_self'}
                    className="block"
                    initial={{ opacity: 0, x: 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ x: 5 }}
                  >
                    <Card hover={false} className="hover:shadow-medium transition-all duration-300">
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-lg bg-neutral-100 flex items-center justify-center ${method.color}`}>
                          <method.icon size={24} />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-neutral-900">{method.title}</h4>
                          <p className="text-sm text-neutral-600 mb-1">{method.description}</p>
                          <p className={`text-sm font-medium ${method.color}`}>{method.value}</p>
                        </div>
                      </div>
                    </Card>
                  </motion.a>
                ))}
              </div>
            </AnimatedSection>

            {/* Office Information */}
            <AnimatedSection animation="slideLeft" delay={0.2}>
              <h3 className="text-2xl font-semibold mb-6">Office Information</h3>
              <Card>
                <div className="space-y-4">
                  {officeInfo.map((info) => (
                    <div key={info.title} className="flex items-center space-x-3">
                      <info.icon className="w-5 h-5 text-primary-600" />
                      <div>
                        <span className="text-sm text-neutral-600">{info.title}: </span>
                        <span className="font-medium text-neutral-900">{info.value}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </AnimatedSection>

            {/* FAQ */}
            <AnimatedSection animation="slideLeft" delay={0.4}>
              <h3 className="text-2xl font-semibold mb-6">Quick FAQ</h3>
              <Card>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-neutral-900 mb-1">How quickly can you start?</h4>
                    <p className="text-sm text-neutral-600">We can typically start within 1-2 weeks after project approval.</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-neutral-900 mb-1">Do you offer maintenance?</h4>
                    <p className="text-sm text-neutral-600">Yes, we provide ongoing maintenance and support packages.</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-neutral-900 mb-1">What's your payment structure?</h4>
                    <p className="text-sm text-neutral-600">We work with milestone-based payments for transparency and security.</p>
                  </div>
                </div>
              </Card>
            </AnimatedSection>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
